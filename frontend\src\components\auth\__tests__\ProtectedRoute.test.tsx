/// <reference types="jest" />
import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import ProtectedRoute from '../ProtectedRoute';
import { useAuthStore } from '../../../store/authStore';

// Mock the auth store
jest.mock('../../../store/authStore');
const mockUseAuthStore = useAuthStore as unknown as jest.Mock;

// Mock the loading spinner
jest.mock('../../common/LoadingSpinner', () => ({
  PageLoading: () => <div>Loading...</div>
}));

const MockComponent = () => <div>Protected Content</div>;

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('ProtectedRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows loading when auth is loading', () => {
    mockUseAuthStore.mockReturnValue({
      isLoading: true,
      user: null,
      isAuthenticated: false,
    } as any);

    renderWithRouter(
      <ProtectedRoute>
        <MockComponent />
      </ProtectedRoute>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('redirects to login when not authenticated', () => {
    mockUseAuthStore.mockReturnValue({
      isLoading: false,
      user: null,
      isAuthenticated: false,
    } as any);

    renderWithRouter(
      <ProtectedRoute>
        <MockComponent />
      </ProtectedRoute>
    );

    // Should redirect to login, so protected content should not be visible
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });

  it('renders children when authenticated as regular user', () => {
    mockUseAuthStore.mockReturnValue({
      isLoading: false,
      user: { id: '1', role: 'USER' },
      isAuthenticated: true,
    } as any);

    renderWithRouter(
      <ProtectedRoute>
        <MockComponent />
      </ProtectedRoute>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('renders children when authenticated as admin', () => {
    mockUseAuthStore.mockReturnValue({
      isLoading: false,
      user: { id: '1', role: 'ADMIN' },
      isAuthenticated: true,
    } as any);

    renderWithRouter(
      <ProtectedRoute>
        <MockComponent />
      </ProtectedRoute>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('renders children when authenticated as moderator', () => {
    mockUseAuthStore.mockReturnValue({
      isLoading: false,
      user: { id: '1', role: 'MODERATOR' },
      isAuthenticated: true,
    } as any);

    renderWithRouter(
      <ProtectedRoute>
        <MockComponent />
      </ProtectedRoute>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('redirects when user does not have admin role but admin is required', () => {
    mockUseAuthStore.mockReturnValue({
      isLoading: false,
      user: { id: '1', role: 'USER' },
      isAuthenticated: true,
    } as any);

    renderWithRouter(
      <ProtectedRoute requireAdmin>
        <MockComponent />
      </ProtectedRoute>
    );

    // Should redirect to home, so protected content should not be visible
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });

  it('renders children when user has admin role and admin is required', () => {
    mockUseAuthStore.mockReturnValue({
      isLoading: false,
      user: { id: '1', role: 'ADMIN' },
      isAuthenticated: true,
    } as any);

    renderWithRouter(
      <ProtectedRoute requireAdmin>
        <MockComponent />
      </ProtectedRoute>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('renders children when user has moderator role and admin is required', () => {
    mockUseAuthStore.mockReturnValue({
      isLoading: false,
      user: { id: '1', role: 'MODERATOR' },
      isAuthenticated: true,
    } as any);

    renderWithRouter(
      <ProtectedRoute requireAdmin>
        <MockComponent />
      </ProtectedRoute>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });
});