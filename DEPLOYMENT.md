# 大学生书籍交易平台 - 部署指南

## 项目概述

这是一个完整的大学生书籍交易平台，包含前端React应用和后端Node.js API服务，支持书籍买卖、拍卖、支付、订单管理等功能。

## 技术栈

### 后端技术栈
- **运行时**: Node.js 18+
- **框架**: Express.js + TypeScript
- **数据库**: MySQL 8.0
- **ORM**: Prisma
- **缓存**: Redis
- **认证**: JWT
- **实时通信**: Socket.io
- **支付**: 支付宝/微信支付模拟
- **邮件**: Nodemailer

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI库**: Ant Design
- **状态管理**: Zustand
- **路由**: React Router v6
- **HTTP客户端**: Axios

## 环境要求

- Node.js 18.0+
- MySQL 8.0+
- Redis 6.0+
- npm 或 yarn

## 安装部署

### 1. 克隆项目

```bash
cd D:\claude镜像\收书卖书
```

### 2. 安装依赖

```bash
# 安装根目录依赖
npm install

# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 3. 数据库设置

#### 3.1 创建MySQL数据库

```sql
CREATE DATABASE bookmarket DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'bookmarket'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON bookmarket.* TO 'bookmarket'@'localhost';
FLUSH PRIVILEGES;
```

#### 3.2 配置环境变量

在 `backend` 目录下创建 `.env` 文件：

```env
# 基础配置
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000
FRONTEND_URL=http://localhost:5173

# 数据库配置
DATABASE_URL="mysql://bookmarket:your_password@localhost:3306/bookmarket"

# Redis配置
REDIS_URL=redis://localhost:6379

# JWT密钥
JWT_SECRET=your_super_secret_jwt_key_here

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# 支付宝配置（可选，用于测试）
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_private_key
ALIPAY_PUBLIC_KEY=alipay_public_key

# 微信支付配置（可选，用于测试）
WECHAT_APP_ID=your_wechat_app_id
WECHAT_MCH_ID=your_merchant_id
WECHAT_API_KEY=your_api_key
```

#### 3.3 初始化数据库

```bash
cd backend

# 生成Prisma客户端
npx prisma generate

# 运行数据库迁移
npx prisma db push

# （可选）种子数据
npx prisma db seed
```

### 4. 启动服务

#### 4.1 启动Redis

```bash
# Windows (如果安装了Redis)
redis-server

# 或使用Docker
docker run -d --name redis -p 6379:6379 redis:6-alpine
```

#### 4.2 启动后端服务

```bash
cd backend
npm run dev
```

后端服务将在 `http://localhost:3000` 启动

#### 4.3 启动前端服务

```bash
cd frontend
npm run dev
```

前端应用将在 `http://localhost:5173` 启动

### 5. 验证部署

访问 `http://localhost:5173` 查看前端应用
访问 `http://localhost:3000/health` 查看后端健康状态

## Docker部署

### 1. 使用Docker Compose

```bash
# 在项目根目录执行
docker-compose up -d
```

### 2. 查看服务状态

```bash
docker-compose ps
```

### 3. 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

## 生产环境配置

### 1. 环境变量配置

```env
NODE_ENV=production
PORT=3000
API_BASE_URL=https://api.yourdomain.com
FRONTEND_URL=https://yourdomain.com

# 数据库配置（生产环境）
DATABASE_URL="mysql://username:password@your-db-host:3306/bookmarket"

# Redis配置（生产环境）
REDIS_URL=redis://your-redis-host:6379

# 强密钥
JWT_SECRET=your_very_secure_production_jwt_secret

# 生产邮件配置
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-production-email
SMTP_PASS=your-production-password
```

### 2. 构建生产版本

```bash
# 构建前端
cd frontend
npm run build

# 构建后端
cd ../backend
npm run build
```

### 3. 部署到云服务器

```bash
# 上传到服务器后
npm install --production
npm run start
```

## API文档

### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh-token` - 刷新令牌
- `GET /api/v1/auth/me` - 获取当前用户信息

### 书籍管理
- `GET /api/v1/books/search` - 搜索书籍
- `GET /api/v1/books/:id` - 获取书籍详情
- `POST /api/v1/books` - 发布书籍
- `PUT /api/v1/books/:id` - 更新书籍
- `DELETE /api/v1/books/:id` - 删除书籍

### 拍卖系统
- `GET /api/v1/auctions/search` - 搜索拍卖
- `GET /api/v1/auctions/:id` - 获取拍卖详情
- `POST /api/v1/auctions` - 创建拍卖
- `POST /api/v1/auctions/:id/bid` - 参与竞拍

### 订单管理
- `POST /api/v1/orders` - 创建订单
- `GET /api/v1/orders/my/orders` - 获取我的订单
- `POST /api/v1/orders/:id/cancel` - 取消订单
- `POST /api/v1/orders/:id/confirm` - 确认收货

### 支付系统
- `POST /api/v1/payments` - 创建支付
- `GET /api/v1/payments/:id/status` - 查询支付状态
- `POST /api/v1/payments/refund` - 申请退款

### 搜索功能
- `GET /api/v1/search/advanced` - 高级搜索
- `GET /api/v1/search/suggestions` - 搜索建议
- `GET /api/v1/search/hot-keywords` - 热门搜索词

## WebSocket事件

### 拍卖相关事件
- `authenticate` - 用户认证
- `join-auction` - 加入拍卖房间
- `leave-auction` - 离开拍卖房间
- `place-bid` - 实时出价
- `new-bid` - 新出价通知
- `auction-ending-soon` - 拍卖即将结束
- `auction-ended` - 拍卖结束

## 监控和日志

### 1. 健康检查
- `GET /health` - 基本健康检查
- 包含数据库连接状态
- 包含Redis连接状态

### 2. 日志配置
- 使用Winston进行日志记录
- 支持不同级别的日志
- 生产环境日志轮转

### 3. 错误监控
- 统一错误处理中间件
- 详细的错误日志记录
- 用户友好的错误响应

## 性能优化

### 1. 数据库优化
- 合理的索引设计
- 查询优化
- 连接池配置

### 2. 缓存策略
- Redis缓存热点数据
- 会话管理
- 搜索结果缓存

### 3. 前端优化
- 代码分割
- 图片懒加载
- CDN静态资源

## 安全考虑

### 1. 认证安全
- JWT令牌机制
- 刷新令牌轮换
- 密码加密存储

### 2. API安全
- 请求限流
- 输入验证
- SQL注入防护

### 3. 数据安全
- 敏感数据脱敏
- HTTPS传输
- 定期备份

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证数据库配置
   - 确认网络连接

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis配置
   - 检查防火墙设置

3. **前端无法访问后端**
   - 检查CORS配置
   - 验证API地址配置
   - 查看网络请求错误

4. **WebSocket连接问题**
   - 检查Socket.io配置
   - 验证防火墙设置
   - 查看浏览器控制台错误

### 调试技巧

1. **后端调试**
   ```bash
   # 查看详细日志
   DEBUG=* npm run dev
   
   # 数据库查询日志
   DEBUG=prisma:query npm run dev
   ```

2. **前端调试**
   - 使用浏览器开发者工具
   - 检查网络请求
   - 查看控制台错误信息

## 联系支持

如有部署问题，请检查：
1. 系统要求是否满足
2. 环境变量是否正确配置
3. 服务依赖是否正常运行
4. 日志文件中的错误信息

项目已完成所有核心功能模块的开发，可以正常启动和运行。