{"name": "rc-input-number", "version": "9.5.0", "description": "React input-number component", "keywords": ["react", "react-component", "react-input-number", "input-number"], "homepage": "https://github.com/react-component/input-number", "bugs": {"url": "https://github.com/react-component/input-number/issues"}, "repository": {"type": "git", "url": "**************:react-component/input-number.git"}, "license": "MIT", "author": "<EMAIL>", "main": "./lib/index", "module": "./es/index", "types": "./es/index.d.ts", "files": ["lib", "es", "assets/*.css"], "scripts": {"compile": "father build && lessc assets/index.less assets/index.css", "coverage": "rc-test --coverage", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "now-build": "npm run docs:build", "prepare": "husky install", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch=antd-5.x", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/mini-decimal": "^1.0.1", "classnames": "^2.2.5", "rc-input": "~1.8.0", "rc-util": "^5.40.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.1", "@swc-node/jest": "^1.5.5", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.0.0", "@types/classnames": "^2.2.9", "@types/jest": "^29.2.4", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/responselike": "^1.0.0", "@umijs/fabric": "^4.0.1", "@umijs/test": "^4.0.36", "cross-env": "^7.0.3", "dumi": "^2.0.13", "eslint": "^8.54.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-unicorn": "^56.0.0", "expect.js": "~0.3.1", "father": "^4.1.1", "glob": "^11.0.0", "husky": "^8.0.3", "jest-environment-jsdom": "^29.3.1", "less": "^4.1.3", "lint-staged": "^15.1.0", "np": "^10.0.5", "rc-test": "^7.0.14", "rc-tooltip": "^6.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "regenerator-runtime": "^0.14.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}