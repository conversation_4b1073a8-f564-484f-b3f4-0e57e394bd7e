{"name": "book-trading-backend", "version": "1.0.0", "description": "书籍交易平台后端API服务", "main": "dist/server.js", "scripts": {"dev": "nodemon --exec ts-node -r tsconfig-paths/register src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.2.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "nodemailer": "^6.9.4", "rate-limiter-flexible": "^2.4.2", "redis": "^4.6.8", "sharp": "^0.32.5", "socket.io": "^4.7.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/node-cron": "^3.0.8", "@types/nodemailer": "^6.4.9", "@types/supertest": "^2.0.12", "jest": "^29.6.2", "nodemon": "^3.0.1", "prisma": "^5.2.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}}