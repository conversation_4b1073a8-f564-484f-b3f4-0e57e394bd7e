{"name": "rc-mentions", "version": "2.20.0", "description": "React Mentions", "keywords": ["react", "react-component", "react-mentions", "mentions"], "homepage": "http://github.com/react-component/mentions", "bugs": {"url": "http://github.com/react-component/mentions/issues"}, "repository": {"type": "git", "url": "**************:react-component/mentions.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "files": ["assets/*.css", "es", "dist", "lib"], "scripts": {"compile": "father build", "coverage": "rc-test --coverage", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "gh-pages": "npm run docs:build && npm run docs:deploy", "lint": "eslint src/ --ext .tsx,.ts", "prepare": "husky install", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch=antd-5.x", "postpublish": "npm run gh-pages", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "dependencies": {"@babel/runtime": "^7.22.5", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.6", "rc-input": "~1.8.0", "rc-menu": "~9.16.0", "rc-textarea": "~1.10.0", "rc-util": "^5.34.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@types/classnames": "^2.2.6", "@types/react": "^18.0.8", "@types/react-dom": "^18.0.3", "@types/warning": "^3.0.0", "@umijs/fabric": "^3.0.0", "dumi": "^2.0.18", "eslint": "^7.0.0", "father": "^4.0.0", "gh-pages": "^5.0.0", "husky": "^9.1.6", "lint-staged": "^15.2.7", "np": "^10.0.0", "prettier": "^3.3.2", "rc-test": "^7.0.14", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.0.4"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}