{"name": "book-trading-frontend", "version": "1.0.0", "type": "module", "description": "书籍交易平台前端应用", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@hookform/resolvers": "^3.3.1", "@tanstack/react-query": "^4.32.6", "antd": "^5.26.6", "axios": "^1.5.0", "dayjs": "^1.11.9", "framer-motion": "^10.16.1", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-intersection-observer": "^9.5.2", "react-router-dom": "^6.15.0", "react-virtualized": "^9.22.5", "recharts": "^2.8.0", "socket.io-client": "^4.7.2", "yup": "^1.2.0", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.22.7", "@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@testing-library/jest-dom": "^6.1.2", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.14", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-virtualized": "^9.21.21", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "axios-mock-adapter": "^1.22.0", "babel-jest": "^29.6.2", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "ts-jest": "^29.1.1", "typescript": "^5.0.2", "vite": "^4.4.5"}}