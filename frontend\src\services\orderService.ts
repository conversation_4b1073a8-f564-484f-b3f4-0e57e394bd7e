import { apiService } from './api';
import { API_ENDPOINTS } from '../constants';
import type {
  Order,
  CreateOrderRequest,
  PaginatedResponse,
} from '../types';

export interface OrderResponse extends Order {}

export interface OrderListResponse extends PaginatedResponse<Order> {}

export interface OrderStats {
  totalOrders: number;
  totalSpent: number;
  totalEarned: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  averageOrderValue: number;
  ordersByMonth: Array<{
    month: string;
    count: number;
    amount: number;
  }>;
}

class OrderService {
  // 获取订单列表
  async getOrders(params?: {
    type?: 'DIRECT' | 'AUCTION';
    status?: 'PENDING' | 'PAID' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED';
    role?: 'BUYER' | 'SELLER';
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
  }): Promise<OrderListResponse> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value));
        }
      });
    }

    const url = `${API_ENDPOINTS.ORDERS.LIST}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiService.get<OrderListResponse>(url);
  }

  // 获取订单详情
  async getOrder(id: string): Promise<OrderResponse> {
    return apiService.get<OrderResponse>(API_ENDPOINTS.ORDERS.DETAIL(id));
  }

  // 创建订单
  async createOrder(data: CreateOrderRequest): Promise<OrderResponse> {
    return apiService.post<OrderResponse>(API_ENDPOINTS.ORDERS.CREATE, data);
  }

  // 更新订单
  async updateOrder(id: string, data: {
    shippingAddress?: string;
    shippingMethod?: string;
    notes?: string;
  }): Promise<OrderResponse> {
    return apiService.put<OrderResponse>(API_ENDPOINTS.ORDERS.UPDATE(id), data);
  }

  // 取消订单
  async cancelOrder(id: string, reason?: string): Promise<void> {
    return apiService.patch(API_ENDPOINTS.ORDERS.CANCEL(id), { reason });
  }

  // 确认收货
  async confirmDelivery(id: string): Promise<OrderResponse> {
    return apiService.patch<OrderResponse>(API_ENDPOINTS.ORDERS.CONFIRM_DELIVERY(id));
  }

  // 发货
  async shipOrder(id: string, data: {
    trackingNumber: string;
    shippingCompany: string;
    notes?: string;
  }): Promise<OrderResponse> {
    return apiService.patch<OrderResponse>(`/orders/${id}/ship`, data);
  }

  // 申请退款
  async requestRefund(id: string, data: {
    reason: string;
    description?: string;
    images?: string[];
  }): Promise<void> {
    return apiService.post(`/orders/${id}/refund`, data);
  }

  // 处理退款申请
  async handleRefund(id: string, data: {
    approved: boolean;
    reason?: string;
    refundAmount?: number;
  }): Promise<void> {
    return apiService.patch(`/orders/${id}/refund`, data);
  }

  // 获取物流信息
  async getTrackingInfo(id: string): Promise<{
    trackingNumber: string;
    shippingCompany: string;
    status: string;
    updates: Array<{
      time: string;
      location: string;
      description: string;
    }>;
  }> {
    return apiService.get(`/orders/${id}/tracking`);
  }

  // 评价订单
  async rateOrder(id: string, data: {
    rating: number;
    comment?: string;
    images?: string[];
  }): Promise<void> {
    return apiService.post(`/orders/${id}/rate`, data);
  }

  // 获取订单评价
  async getOrderRating(id: string): Promise<{
    id: string;
    orderId: string;
    rating: number;
    comment?: string;
    images: string[];
    createdAt: string;
    response?: {
      comment: string;
      createdAt: string;
    };
  }> {
    return apiService.get(`/orders/${id}/rating`);
  }

  // 回复评价
  async replyToRating(id: string, comment: string): Promise<void> {
    return apiService.post(`/orders/${id}/rating/reply`, { comment });
  }

  // 获取订单统计
  async getOrderStats(): Promise<OrderStats> {
    return apiService.get<OrderStats>('/orders/stats');
  }

  // 导出订单
  async exportOrders(params?: {
    type?: 'DIRECT' | 'AUCTION';
    status?: string;
    role?: 'BUYER' | 'SELLER';
    startDate?: string;
    endDate?: string;
    format?: 'csv' | 'excel';
  }): Promise<void> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value));
        }
      });
    }

    const url = `/orders/export${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiService.download(url, `orders_${new Date().toISOString().split('T')[0]}.${params?.format || 'csv'}`);
  }

  // 批量更新订单状态
  async batchUpdateOrderStatus(ids: string[], status: string): Promise<void> {
    return apiService.patch('/orders/batch/status', { ids, status });
  }

  // 获取买家购买历史
  async getBuyerHistory(buyerId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<OrderListResponse> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== ''as any) {
          queryParams.append(key, String(value));
        }
      });
    }

    const url = `/orders/buyer/${buyerId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiService.get<OrderListResponse>(url);
  }

  // 获取卖家销售历史
  async getSellerHistory(sellerId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<OrderListResponse> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== ''as any) {
          queryParams.append(key, String(value));
        }
      });
    }

    const url = `/orders/seller/${sellerId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiService.get<OrderListResponse>(url);
  }

  // 计算订单费用
  async calculateOrderCost(data: {
    bookId?: string;
    auctionId?: string;
    shippingMethod: string;
    couponCode?: string;
  }): Promise<{
    bookPrice: number;
    shippingFee: number;
    discount: number;
    couponDiscount: number;
    totalAmount: number;
    breakdown: Array<{
      name: string;
      amount: number;
    }>;
  }> {
    return apiService.post('/orders/calculate', data);
  }

  // 应用优惠券
  async applyCoupon(couponCode: string, orderData: {
    bookId?: string;
    auctionId?: string;
  }): Promise<{
    valid: boolean;
    discount: number;
    description: string;
  }> {
    return apiService.post('/orders/coupon/apply', {
      couponCode,
      ...orderData,
    });
  }

  // 获取可用优惠券
  async getAvailableCoupons(): Promise<Array<{
    id: string;
    code: string;
    name: string;
    description: string;
    discount: number;
    discountType: 'FIXED' | 'PERCENTAGE';
    minAmount: number;
    maxDiscount?: number;
    expiresAt: string;
  }>> {
    return apiService.get('/orders/coupons/available');
  }

  // 重新下单
  async reorder(id: string): Promise<OrderResponse> {
    return apiService.post(`/orders/${id}/reorder`);
  }

  // 获取订单发票
  async getInvoice(id: string): Promise<{
    invoiceNumber: string;
    invoiceUrl: string;
  }> {
    return apiService.get(`/orders/${id}/invoice`);
  }

  // 下载订单发票
  async downloadInvoice(id: string): Promise<void> {
    return apiService.download(`/orders/${id}/invoice/download`, `invoice_${id}.pdf`);
  }
}

export const orderService = new OrderService();
export default orderService;